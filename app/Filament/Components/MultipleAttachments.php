<?php

namespace App\Filament\Components;

use App\Models\Attachment;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Database\Eloquent\Model;

class MultipleAttachments extends Field
{
    protected string $view = 'filament.components.multiple-attachments';

    protected string $disk = 's3';
    protected string $directory = 'finance/attachments';
    protected array $acceptedFileTypes = [];
    protected int $maxFiles = 10;
    protected ?string $maxSize = null;

    public function disk(string $disk): static
    {
        $this->disk = $disk;
        return $this;
    }

    public function directory(string $directory): static
    {
        $this->directory = $directory;
        return $this;
    }

    public function acceptedFileTypes(array $types): static
    {
        $this->acceptedFileTypes = $types;
        return $this;
    }

    public function maxFiles(int $maxFiles): static
    {
        $this->maxFiles = $maxFiles;
        return $this;
    }

    public function maxSize(?string $maxSize): static
    {
        $this->maxSize = $maxSize;
        return $this;
    }

    public function getDisk(): string
    {
        return $this->disk;
    }

    public function getDirectory(): string
    {
        return $this->directory;
    }

    public function getAcceptedFileTypes(): array
    {
        return $this->acceptedFileTypes;
    }

    public function getMaxFiles(): int
    {
        return $this->maxFiles;
    }

    public function getMaxSize(): ?string
    {
        return $this->maxSize;
    }

    public function getChildComponents(): array
    {
        return [
            Section::make('Attachments')
                ->schema([
                    ViewField::make('existing_attachments')
                        ->view('filament.components.existing-attachments')
                        ->viewData([
                            'attachments' => fn (Get $get): array => $this->getExistingAttachments($get),
                        ])
                        ->visible(fn (Get $get) => !empty($this->getExistingAttachments($get))),

                    Repeater::make('new_attachments')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    FileUpload::make('file')
                                        ->required()
                                        ->disk($this->disk)
                                        ->directory($this->directory)
                                        ->acceptedFileTypes($this->acceptedFileTypes)
                                        ->maxSize($this->maxSize)
                                        ->imageResizeTargetWidth('720')
                                        ->imageResizeTargetHeight('720')
                                        ->imageResizeMode('contain'),

                                    TextInput::make('title')
                                        ->placeholder('Optional title for this attachment'),
                                ]),

                            Textarea::make('description')
                                ->placeholder('Optional description for this attachment')
                                ->rows(2),
                        ])
                        ->addActionLabel('Add Attachment')
                        ->maxItems($this->maxFiles)
                        ->collapsible()
                        ->itemLabel(fn (array $state): ?string => $state['title'] ?? 'New Attachment')
                        ->defaultItems(0)
                        ->grid(1),
                ])
                ->collapsible()
                ->collapsed(fn (Get $get) => empty($this->getExistingAttachments($get))),
        ];
    }

    protected function getExistingAttachments(Get $get): array
    {
        $record = $this->getRecord();

        if (!$record || !method_exists($record, 'attachments')) {
            return [];
        }

        return $record->attachments()
            ->get()
            ->map(function (Attachment $attachment) {
                return [
                    'id' => $attachment->id,
                    'title' => $attachment->display_name,
                    'file_name' => $attachment->original_file_name,
                    'file_size' => $attachment->file_size_human,
                    'mime_type' => $attachment->mime_type,
                    'is_image' => $attachment->is_image,
                    'url' => $attachment->url,
                    'download_url' => $attachment->download_url,
                    'description' => $attachment->description,
                ];
            })
            ->toArray();
    }

    public function saveRelationships(): void
    {
        $record = $this->getRecord();
        $state = $this->getState();

        if (!$record || !isset($state['new_attachments'])) {
            return;
        }

        foreach ($state['new_attachments'] as $attachmentData) {
            if (!isset($attachmentData['file']) || empty($attachmentData['file'])) {
                continue;
            }

            $filePath = $attachmentData['file'];
            $fileName = basename($filePath);

            $record->addAttachmentFromPath(
                path: $filePath,
                originalName: $fileName,
                title: $attachmentData['title'] ?? null,
                description: $attachmentData['description'] ?? null,
                disk: $this->disk
            );
        }
    }

    public function deleteAttachment(int $attachmentId): void
    {
        $record = $this->getRecord();

        if (!$record || !method_exists($record, 'removeAttachment')) {
            return;
        }

        $record->removeAttachment($attachmentId);
    }
}
