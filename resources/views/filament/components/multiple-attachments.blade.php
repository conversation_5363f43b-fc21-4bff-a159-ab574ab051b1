<div
    x-data="{
        attachments: @entangle($getStatePath()),
        deleteAttachment(id) {
            $wire.call('deleteAttachment', id);
        }
    }"
    {{ $attributes->merge(['class' => 'space-y-4']) }}
>
    @php
        $existingAttachments = $getRecord()?->attachments ?? collect();
        $hasExistingAttachments = $existingAttachments->isNotEmpty();
    @endphp

    @if ($hasExistingAttachments)
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Current Attachments ({{ $existingAttachments->count() }})
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                @foreach ($existingAttachments as $attachment)
                    <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="flex-shrink-0 mr-3">
                            @if ($attachment->is_image)
                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @else
                                <div class="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ $attachment->display_name }}
                                </h4>
                                <div class="flex items-center space-x-1 ml-2">
                                    @if ($attachment->is_image)
                                        <button
                                            type="button"
                                            onclick="window.open('{{ $attachment->url }}', '_blank')"
                                            class="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 rounded"
                                            title="View image"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </button>
                                    @endif

                                    <a
                                        href="{{ $attachment->download_url }}"
                                        download="{{ $attachment->original_file_name }}"
                                        class="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 rounded"
                                        title="Download file"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </a>

                                    <button
                                        type="button"
                                        x-on:click="deleteAttachment({{ $attachment->id }})"
                                        x-confirm="Are you sure you want to delete this attachment?"
                                        class="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 rounded"
                                        title="Delete attachment"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ $attachment->original_file_name }} • {{ $attachment->file_size_human }}
                            </p>

                            @if ($attachment->description)
                                <p class="text-xs text-gray-600 dark:text-gray-300 mt-1">
                                    {{ $attachment->description }}
                                </p>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Add New Attachments
        </h3>

        {{ $getChildComponentContainer() }}
    </div>
</div>
